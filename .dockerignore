# ============================================================================
# Docker 构建忽略文件
# ============================================================================

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# 环境变量和配置文件
.env
.env.*
!.env.example

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 版本控制
.git/
.gitignore
.gitattributes

# 文档
CHANGELOG.md
LICENSE
*.md
!README.md
!CLAUDE.md

# 测试和覆盖率
.coverage
htmlcov/
.tox/
.pytest_cache/
.coverage.*

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# MacOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# 临时文件
*.tmp
*.temp
*.bak

# 节点模块 (如果有前端组件)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# 开发和测试脚本
start_server_uv.py
test_api_uv.py
demo_local.py
create_admin.py

# 项目特定文件
文档重写总结.md
项目总结.md

# Docker 镜像
docker-images/