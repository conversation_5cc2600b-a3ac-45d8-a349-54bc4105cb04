"""
数据模式定义模块

定义所有数据结构和验证模式
"""

# 航班数据分析输出模式
FLIGHT_DATA_ANALYSIS_SCHEMA = {
    "type": "object",
    "properties": {
        "basic_stats": {
            "type": "object",
            "properties": {
                "total_flights": {"type": "integer"},
                "unique_airlines": {"type": "integer"},
                "unique_aircraft_types": {"type": "integer"},
                "date_range": {
                    "type": "object",
                    "properties": {
                        "start_date": {"type": "string"},
                        "end_date": {"type": "string"}
                    }
                }
            }
        },
        "temporal_patterns": {
            "type": "object",
            "properties": {
                "hourly_distribution": {"type": "object"},
                "daily_distribution": {"type": "object"},
                "peak_hours": {"type": "array", "items": {"type": "integer"}},
                "off_peak_hours": {"type": "array", "items": {"type": "integer"}}
            }
        },
        "punctuality_metrics": {
            "type": "object",
            "properties": {
                "on_time_percentage": {"type": "number"},
                "average_delay_minutes": {"type": "number"},
                "delay_distribution": {"type": "object"}
            }
        },
        "duration_patterns": {
            "type": "object",
            "properties": {
                "average_duration_minutes": {"type": "number"},
                "duration_range": {
                    "type": "object",
                    "properties": {
                        "min_minutes": {"type": "integer"},
                        "max_minutes": {"type": "integer"}
                    }
                }
            }
        },
        "important_flights": {
            "type": "object",
            "properties": {
                "most_frequent_flight_numbers": {"type": "array"},
                "airlines_with_most_flights": {"type": "array"}
            }
        },
        "route_efficiency": {
            "type": "object",
            "properties": {
                "efficiency_score": {"type": "number"},
                "distance_analysis": {"type": "object"}
            }
        },
        "popular_destinations": {
            "type": "object",
            "properties": {
                "top_destinations": {"type": "array"},
                "destination_distribution": {"type": "object"}
            }
        },
        "network_analysis": {
            "type": "object",
            "properties": {
                "airline_diversity": {"type": "object"},
                "route_connectivity": {"type": "object"}
            }
        }
    }
}