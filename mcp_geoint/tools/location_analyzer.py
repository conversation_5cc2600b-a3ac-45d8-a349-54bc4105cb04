"""
地点航班分析工具

分析从单个机场、城市或国家出发或到达的航班运营情况
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, Literal
from typing_extensions import Annotated
from pydantic import Field

from .server import mcp
from ..utils import handle_api_errors
from ..api_client import make_location_flight_request, resolve_airport_location
from ..analyzers import FlightAnalyzer, TemporalAnalyzer, PunctualityAnalyzer, RouteAnalyzer

logger = logging.getLogger(__name__)


@mcp.tool
@handle_api_errors
async def analyze_flight_location(
    location: Annotated[str, Field(description="地点名称 - 支持中文城市名、国家名、机场名称或机场代码，例如 '北京', '中国', '首都国际机场', 'ZBAA', 'PEK'")],
    location_type: Annotated[Literal["city", "country", "airport"], Field(description="地点类型: 'city', 'country', 或 'airport'")],
    date_from: Annotated[str, Field(description="查询开始日期 (YYYY-MM-DD 格式)")],
    date_to: Annotated[str, Field(description="查询结束日期 (YYYY-MM-DD 格式)")],
    direction: Annotated[Literal["from", "to"], Field(description="分析方向: 'from' = 从该地出发, 'to' = 到达该地")],
    is_international: Annotated[Optional[bool], Field(description="是否只看国际航班，None=全部, True=仅国际, False=仅国内", default=None)],
    detail_level: Annotated[str, Field(description="分析详细程度: 'summary'=基础统计 | 'detailed'=深度分析", default="summary")]
) -> Dict[str, Any]:
    """
    分析从单个机场、城市或国家出发或到达的航班运营情况、航线网络和市场格局。

    本工具提供对特定地理位置（机场、城市、国家）的航班流量的全面洞察，是进行区域航空市场分析、
    交通枢纽研究和地理情报评估的强大工具。

    🔍 支持两种分析深度：

    📊 SUMMARY模式 - 基础运营统计：
    • 运营概况：总航班数、涉及的航空公司、对向机场分布
    • 市场主力：运营航班最多的航空公司和飞机型号
    • 航线网络：最繁忙的10条目的地/出发地航线
    • 🆕 热门目的地：最热门的城市和国家（如果有国际航班则包含国际国家）

    📈 DETAILED模式 - 深度运营与网络分析：
    • 全部基础统计
    • 时间模式：每日/每小时的航班量，识别高峰时段
    • 准点率分析：延误情况、平均延误时间、准点率
    • 国际/国内构成：国际与国内航班的比例及各自的主要航线
    • 航空公司多样性：分析各航司在此地的航线覆盖广度和机队多样性
    
    🛫 机场支持：
    • 机场名称：支持中文和英文机场名称模糊查询，如 '首都国际机场', 'Beijing Capital'
    • 机场代码：支持ICAO代码（如 'ZBAA'）和IATA代码（如 'PEK'）
    • 自动解析：机场名称会自动通过搜索API解析为对应的机场代码

    🌍 新增功能 - 热门目的地分析：
    • 最热门城市：按航班数量排序的前10个目的地城市
    • 最热门国家：按航班数量排序的前10个目的地国家
    • 国际航班国家：如果存在国际航班，单独统计国际目的地国家排名
    """
    try:
        # 解析机场位置（如果是机场类型，将名称转换为代码）
        resolved_location, resolved_location_type = await resolve_airport_location(location, location_type)
        
        # 调用专用的地点航班请求函数
        all_flights = await make_location_flight_request(
            location=resolved_location,
            location_type=resolved_location_type,
            date_from=date_from,
            date_to=date_to,
            direction=direction,
            is_international=is_international
        )

        if not all_flights:
            display_location = f"{location}"
            if resolved_location != location and location_type == "airport":
                display_location = f"{location} ({resolved_location})"
            return {
                "summary": f"在指定日期范围和条件下，未找到从 {display_location} 出发/到达的航班数据。",
                "analysis_date": datetime.now().isoformat()
            }

        # --- 开始分析 ---
        basic_stats = FlightAnalyzer.extract_basic_stats(all_flights)
        network_stats = RouteAnalyzer.analyze_network_and_diversity(all_flights, direction)
        popular_destinations = RouteAnalyzer.analyze_popular_destinations(all_flights, direction)

        # 准备基础返回结果
        display_location = f"{location}"
        if resolved_location != location and location_type == "airport":
            display_location = f"{location} ({resolved_location})"
        
        result = {
            "location_analyzed": f"{display_location} ({location_type})",
            "direction": direction,
            "date_range": f"{date_from} to {date_to}",
            "detail_level": detail_level,
            "summary": {
                "total_flights": basic_stats["total_flights"],
                "unique_airlines": basic_stats["unique_airlines"],
                "unique_airports_involved": basic_stats["unique_arrival_airports"] if direction == 'from' else basic_stats["unique_departure_airports"],
            },
            "top_airlines": dict(basic_stats["airlines"].most_common(10)),
            "top_aircraft_models": dict(basic_stats["aircraft_models"].most_common(10)),
            "route_network_analysis": {
                "top_routes": network_stats["top_routes"]
            },
            "most_popular_cities": popular_destinations["most_popular_cities"],
            "most_popular_countries": popular_destinations["most_popular_countries"],
            "analysis_date": datetime.now().isoformat()
        }

        # 如果有国际航班，添加最热门的国际国家
        if popular_destinations["has_international_flights"]:
            result["most_popular_international_countries"] = popular_destinations["most_popular_international_countries"]

        if detail_level == "detailed":
            temporal_data = TemporalAnalyzer.analyze_patterns(all_flights)
            punctuality_data = PunctualityAnalyzer.calculate_metrics(all_flights)
            
            result["temporal_analysis"] = temporal_data
            result["punctuality_analysis"] = punctuality_data
            result["international_domestic_split"] = network_stats["international_domestic_split"]
            result["airline_operational_diversity"] = network_stats["airline_operational_diversity"]

        return result

    except Exception as e:
        logger.error(f"分析 {location} 航班时发生错误: {e}", exc_info=True)
        return {"error": "处理请求时发生错误", "details": str(e)}