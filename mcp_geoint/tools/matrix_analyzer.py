"""
城市矩阵分析工具

分析多个城市间的航班矩阵情况
"""

import logging
from datetime import datetime
from typing import Dict, Any
from collections import defaultdict
from typing_extensions import Annotated
from pydantic import Field

from .server import mcp
from ..utils import handle_api_errors
from ..api_client import fetch_flight_matrix_data

logger = logging.getLogger(__name__)


@mcp.tool
@handle_api_errors
async def analyze_flight_matrix_cities(
    cities: Annotated[str, Field(description="城市列表，用逗号分隔，例如 '北京,上海,广州,深圳'")],
    date_from: Annotated[str, Field(description="查询开始日期 (YYYY-MM-DD 格式)")],
    date_to: Annotated[str, Field(description="查询结束日期 (YYYY-MM-DD 格式)")]
) -> Dict[str, Any]:
    """
    分析多个城市间的航班矩阵情况，获取城市间航班连接的全面统计信息。
    
    该工具通过查询航班矩阵API，分析指定城市列表中任意两个城市之间的航班连接情况，
    提供城市间航空网络的全面洞察，适用于区域航空市场分析和交通网络研究。
    
    🔍 分析内容：
    
    📊 航班连接矩阵：
    • 城市对航班数量：每个城市对之间的航班总数
    • 机场覆盖：出发和到达机场的数量统计
    • 双向连接：分析往返航班的对称性
    
    📈 网络统计摘要：
    • 参与城市：分析的城市总数
    • 航线总数：所有城市对之间的航线数量
    • 航班总量：所有航线的航班总数
    • 连接密度：城市间连接的紧密程度
    
    🎯 热门航线识别：
    • 最繁忙航线：按航班数量排序的城市对
    • 航班分布：各城市对的航班数量分布
    • 机场利用率：各城市的机场使用情况
    
    💡 适用场景：
    • 区域航空网络分析
    • 城市间交通流量研究
    • 航空市场竞争分析
    • 交通枢纽重要性评估
    • 区域经济连接度分析
    
    📋 数据来源：实时航班矩阵API，提供准确的城市间航班统计信息
    """
    try:
        # 验证输入并解析城市列表
        if not cities or not cities.strip():
            return {
                "error": "城市列表不能为空",
                "cities_analyzed": cities,
                "date_range": f"{date_from} to {date_to}",
                "analysis_date": datetime.now().isoformat()
            }
        
        cities_list = [city.strip() for city in cities.split(',') if city.strip()]
        
        if not cities_list:
            return {
                "error": "未提供有效的城市名称",
                "cities_analyzed": cities,
                "date_range": f"{date_from} to {date_to}",
                "analysis_date": datetime.now().isoformat()
            }
        
        # 获取矩阵数据（传递原始字符串和日期参数）
        data = await fetch_flight_matrix_data(cities, date_from, date_to)
        
        if not data:
            return {
                "cities_analyzed": cities,
                "date_range": f"{date_from} to {date_to}",
                "summary": "在指定日期范围内未找到城市间航班数据",
                "analysis_date": datetime.now().isoformat()
            }
        
        # 提取数据
        results = data.get("results", [])
        matrix_summary = data.get("matrix_summary", {})
        
        if not results:
            return {
                "cities_analyzed": cities,
                "date_range": f"{date_from} to {date_to}",
                "summary": "在指定日期范围内未找到城市间航班数据",
                "analysis_date": datetime.now().isoformat()
            }
        
        # 分析航班矩阵数据
        city_pairs = []
        total_flights = 0
        max_flights_route = None
        max_flights_count = 0
        
        # 统计各城市的出发和到达航班
        city_departure_stats = defaultdict(lambda: {"flights": 0, "routes": 0, "airports": 0})
        city_arrival_stats = defaultdict(lambda: {"flights": 0, "routes": 0, "airports": 0})
        
        for route in results:
            from_city = route.get("from_city")
            to_city = route.get("to_city")
            flights_count = route.get("flights_count", 0)
            from_airports_count = route.get("from_airports_count", 0)
            to_airports_count = route.get("to_airports_count", 0)
            
            # 记录城市对信息
            city_pairs.append({
                "route": f"{from_city} → {to_city}",
                "flights_count": flights_count,
                "from_airports_count": from_airports_count,
                "to_airports_count": to_airports_count
            })
            
            total_flights += flights_count
            
            # 找出最繁忙的航线
            if flights_count > max_flights_count:
                max_flights_count = flights_count
                max_flights_route = f"{from_city} → {to_city}"
            
            # 统计各城市的出发情况
            city_departure_stats[from_city]["flights"] += flights_count
            city_departure_stats[from_city]["routes"] += 1
            city_departure_stats[from_city]["airports"] = max(
                city_departure_stats[from_city]["airports"], 
                from_airports_count
            )
            
            # 统计各城市的到达情况
            city_arrival_stats[to_city]["flights"] += flights_count
            city_arrival_stats[to_city]["routes"] += 1
            city_arrival_stats[to_city]["airports"] = max(
                city_arrival_stats[to_city]["airports"], 
                to_airports_count
            )
        
        # 按航班数量排序城市对
        city_pairs_sorted = sorted(city_pairs, key=lambda x: x["flights_count"], reverse=True)
        
        # 分析城市连接度
        cities_count = len(cities_list)
        possible_routes = cities_count * (cities_count - 1) if cities_count > 1 else 0
        actual_routes = len(results)
        from ..utils import safe_divide
        connectivity_rate = round(safe_divide(actual_routes, possible_routes) * 100, 2)
        
        # 计算平均航班数
        avg_flights_per_route = round(safe_divide(total_flights, actual_routes), 2)
        
        # 找出最活跃的城市（按总航班数）
        city_total_activity = defaultdict(int)
        for city in cities_list:
            departure_flights = city_departure_stats[city]["flights"]
            arrival_flights = city_arrival_stats[city]["flights"]
            city_total_activity[city] = departure_flights + arrival_flights
        
        most_active_city = max(city_total_activity.items(), key=lambda x: x[1]) if city_total_activity else None
        
        # 构建返回结果
        result = {
            "cities_analyzed": cities,
            "date_range": f"{date_from} to {date_to}",
            "matrix_summary": {
                "cities_count": matrix_summary.get("cities_count", cities_count),
                "total_routes": matrix_summary.get("total_routes", actual_routes),
                "total_flights": matrix_summary.get("total_flights", total_flights),
                "cities_list": cities_list,
                "connectivity_rate_percent": connectivity_rate,
                "average_flights_per_route": avg_flights_per_route
            },
            "busiest_routes": city_pairs_sorted[:10],  # 前10条最繁忙航线
            "busiest_route": {
                "route": max_flights_route,
                "flights_count": max_flights_count
            } if max_flights_route else None,
            "most_active_city": {
                "city": most_active_city[0] if most_active_city else None,
                "total_flights": most_active_city[1] if most_active_city else 0
            },
            "city_departure_analysis": dict(sorted(
                city_departure_stats.items(), 
                key=lambda x: x[1]["flights"], 
                reverse=True
            )),
            "city_arrival_analysis": dict(sorted(
                city_arrival_stats.items(), 
                key=lambda x: x[1]["flights"], 
                reverse=True
            )),
            "route_distribution": {
                "routes_with_high_frequency": len([r for r in results if r.get("flights_count", 0) > avg_flights_per_route]),
                "routes_with_low_frequency": len([r for r in results if r.get("flights_count", 0) <= avg_flights_per_route]),
                "routes_over_1000_flights": len([r for r in results if r.get("flights_count", 0) > 1000]),
                "routes_under_100_flights": len([r for r in results if r.get("flights_count", 0) < 100])
            },
            "analysis_date": datetime.now().isoformat()
        }
        
        return result
        
    except Exception as e:
        logger.error(f"分析城市矩阵航班时发生错误: {e}", exc_info=True)
        return {
            "error": "处理请求时发生错误",
            "details": str(e),
            "cities_analyzed": cities,
            "date_range": f"{date_from} to {date_to}",
            "analysis_date": datetime.now().isoformat()
        }