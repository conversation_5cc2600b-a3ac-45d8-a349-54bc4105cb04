"""
时间工具

提供获取当前时间的功能，支持时区
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from typing_extensions import Annotated
from pydantic import Field
import pytz

from .server import mcp

logger = logging.getLogger(__name__)


@mcp.tool
async def get_current_time(
    timezone: Annotated[Optional[str], Field(
        description="时区名称，例如 'Asia/Shanghai', 'America/New_York', 'Europe/London' 等。默认为 'Asia/Shanghai'",
        default="Asia/Shanghai"
    )] = "Asia/Shanghai"
) -> str:
    """
    获取当前时间信息。为用户提问提供当前时间/日期的工具。
    
    返回指定时区的当前时间，格式为 YYYY-MM-DD HH:mm:ss。
    
    支持的时区示例：
    - Asia/Shanghai: 中国上海时间
    - Asia/Tokyo: 日本东京时间
    - America/New_York: 美国纽约时间
    - America/Los_Angeles: 美国洛杉矶时间
    - Europe/London: 英国伦敦时间
    - Europe/Paris: 法国巴黎时间
    - UTC: 协调世界时
    
    如果提供的时区无效，将使用默认时区 Asia/Shanghai。
    """
    try:
        # 尝试使用指定的时区
        try:
            tz = pytz.timezone(timezone)
            timezone_used = timezone
        except pytz.exceptions.UnknownTimeZoneError:
            # 如果时区无效，使用默认时区
            tz = pytz.timezone("Asia/Shanghai")
            timezone_used = "Asia/Shanghai"
            logger.warning(f"Unknown timezone '{timezone}', using default 'Asia/Shanghai'")
        
        # 获取当前时间
        now = datetime.now(tz)
        
        # 格式化时间字符串，添加时区信息
        time_str = now.strftime("%Y-%m-%d %H:%M:%S") + f" {timezone_used}"
        
        return time_str
        
    except Exception as e:
        logger.error(f"获取时间信息失败: {repr(e)}")
        return f"❌ 获取时间信息失败: {repr(e)}"
