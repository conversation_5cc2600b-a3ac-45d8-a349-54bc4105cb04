"""
飞行时长分析器

负责分析航班飞行时长模式和异常值
"""

import math
import logging
from typing import Dict, Any, List
from statistics import mean
from ..utils import calculate_flight_duration, safe_divide

logger = logging.getLogger(__name__)


class DurationAnalyzer:
    """飞行时长分析器"""
    
    @staticmethod
    def analyze_patterns(flights: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析航班飞行时长模式和异常值
        
        Args:
            flights: 航班数据列表
            
        Returns:
            包含飞行时长分析的字典
        """
        if not flights:
            return {}
        
        durations = []
        for flight in flights:
            duration = calculate_flight_duration(
                flight.get("departure_actual_time") or flight.get("departure_scheduled_time"),
                flight.get("arrival_actual_time") or flight.get("arrival_scheduled_time")
            )
            if duration and duration > 0:
                durations.append(duration)
        
        if not durations:
            return {"error": "无法计算飞行时长"}
        
        durations.sort()
        avg_duration = mean(durations)
        
        # 计算分位数
        def percentile(data, p):
            if len(data) == 1:
                return data[0]
            k = (len(data) - 1) * p
            f = math.floor(k)
            c = math.ceil(k)
            if f == c:
                return data[int(k)]
            return data[int(f)] * (c - k) + data[int(c)] * (k - f)
        
        p25 = percentile(durations, 0.25)
        p50 = percentile(durations, 0.5)  # 中位数
        p75 = percentile(durations, 0.75)
        
        # 计算异常值范围
        iqr = p75 - p25
        lower_bound = p25 - 1.5 * iqr
        upper_bound = p75 + 1.5 * iqr
        
        outliers = [d for d in durations if d < lower_bound or d > upper_bound]
        
        return {
            "total_flights_analyzed": len(durations),
            "average_duration_minutes": round(avg_duration, 2),
            "median_duration_minutes": round(p50, 2),
            "min_duration_minutes": min(durations),
            "max_duration_minutes": max(durations),
            "percentiles": {
                "p25": round(p25, 2),
                "p50": round(p50, 2),
                "p75": round(p75, 2)
            },
            "outliers": {
                "count": len(outliers),
                "percentage": round(safe_divide(len(outliers), len(durations)) * 100, 2)
            }
        }