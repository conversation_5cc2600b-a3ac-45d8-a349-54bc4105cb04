"""
航班班次分析器

负责分析重要航班班次及其准点率
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from collections import defaultdict
from ..utils import safe_divide

logger = logging.getLogger(__name__)


class FlightNumberAnalyzer:
    """航班班次分析器"""
    
    @staticmethod
    def analyze_important_numbers(flights: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析重要航班班次（按频次排序前10）及其准点率
        
        Args:
            flights: 航班数据列表
            
        Returns:
            包含重要航班班次分析的字典
        """
        if not flights:
            return {}
        
        # 统计每个航班班次的出现频次和准点情况
        flight_number_stats = defaultdict(lambda: {
            "frequency": 0,
            "on_time": 0,
            "delayed": 0,
            "airline": "Unknown",
            "total_analyzed": 0,
            "departure_times": defaultdict(int)  # 统计出发时间分布
        })
        
        for flight in flights:
            flight_number = flight.get("flight_number_iata", "Unknown")
            airline = flight.get("airline_name", "Unknown")
            scheduled = flight.get("departure_scheduled_time")
            actual = flight.get("departure_actual_time")
            
            # 跳过Unknown的航班班次或空值
            if not flight_number or flight_number == "Unknown" or not flight_number.strip():
                continue
                
            # 更新频次和航空公司信息（只在第一次遇到时设置航空公司）
            flight_number_stats[flight_number]["frequency"] += 1
            if flight_number_stats[flight_number]["airline"] == "Unknown":
                flight_number_stats[flight_number]["airline"] = airline
            
            # 统计出发时间分布
            if scheduled:
                try:
                    dt = datetime.fromisoformat(scheduled.replace('Z', '+00:00'))
                    departure_hour = dt.strftime('%H:00')
                    flight_number_stats[flight_number]["departure_times"][departure_hour] += 1
                except (ValueError, TypeError) as e:
                    logger.debug(f"Failed to parse scheduled time '{scheduled}' for flight {flight_number}: {e}")
                    pass
            
            # 分析准点情况
            if scheduled and actual:
                try:
                    sched_dt = datetime.fromisoformat(scheduled.replace('Z', '+00:00'))
                    actual_dt = datetime.fromisoformat(actual.replace('Z', '+00:00'))
                    
                    delay_minutes = (actual_dt - sched_dt).total_seconds() / 60
                    flight_number_stats[flight_number]["total_analyzed"] += 1
                    
                    if delay_minutes > 15:
                        flight_number_stats[flight_number]["delayed"] += 1
                    else:
                        flight_number_stats[flight_number]["on_time"] += 1
                except (ValueError, TypeError) as e:
                    logger.debug(f"Failed to parse times for flight {flight_number} punctuality - scheduled: '{scheduled}', actual: '{actual}': {e}")
                    pass
        
        # 过滤掉频次为0的航班，按频次排序，取前10
        valid_flight_numbers = {k: v for k, v in flight_number_stats.items() if v["frequency"] > 0}
        top_flight_numbers = sorted(
            valid_flight_numbers.items(),
            key=lambda x: x[1]["frequency"],
            reverse=True
        )[:10]
        
        # 计算每个航班班次的准点率
        important_flights = {}
        for flight_number, stats in top_flight_numbers:
            analyzed = stats["total_analyzed"]
            punctuality_rate = None
            if analyzed > 0:
                punctuality_rate = round(safe_divide(stats["on_time"], analyzed) * 100, 2)
            
            # 找出最高频的出发时间
            most_common_departure_time = None
            departure_time_frequency = 0
            if stats["departure_times"]:
                most_common_time, frequency = max(stats["departure_times"].items(), key=lambda x: x[1])
                most_common_departure_time = most_common_time
                departure_time_frequency = frequency
            
            important_flights[flight_number] = {
                "airline": stats["airline"],
                "frequency": stats["frequency"],
                "punctuality_rate_percent": punctuality_rate,
                "on_time_flights": stats["on_time"],
                "delayed_flights": stats["delayed"],
                "total_analyzed_flights": analyzed,
                "most_common_departure_time": most_common_departure_time,
                "departure_time_frequency": departure_time_frequency
            }
        
        return {
            "total_unique_flight_numbers": len(valid_flight_numbers),
            "important_flight_numbers": important_flights
        }