"""
基础航班数据分析器

负责提取基本的航班统计信息
"""

import logging
from typing import Dict, Any, List
from collections import Counter
from statistics import mean
from ..utils import calculate_flight_duration

logger = logging.getLogger(__name__)


class FlightAnalyzer:
    """基础航班数据分析器"""
    
    @staticmethod
    def extract_basic_stats(flights: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        提取基本航班统计信息
        
        Args:
            flights: 航班数据列表
            
        Returns:
            包含基本统计信息的字典
        """
        if not flights:
            return {}
        
        # 基本统计
        airlines = Counter(flight.get("airline_name", "Unknown") for flight in flights)
        airports_from = Counter((flight.get("from_airport_info") or {}).get("name", "Unknown") for flight in flights)
        airports_to = Counter((flight.get("to_airport_info") or {}).get("name", "Unknown") for flight in flights)
        aircraft_models = Counter(
            model_name for flight in flights
            if (model_name := (flight.get("aircraft_info") or {}).get("model_name"))
            and model_name not in ("Unknown", "None", None)
        )
        flight_statuses = Counter(flight.get("status", "unknown") for flight in flights)
        
        # 计算平均飞行时间
        durations = []
        for flight in flights:
            duration = calculate_flight_duration(
                flight.get("departure_actual_time") or flight.get("departure_scheduled_time"),
                flight.get("arrival_actual_time") or flight.get("arrival_scheduled_time")
            )
            if duration:
                durations.append(duration)
        
        avg_duration = mean(durations) if durations else None
        
        return {
            "total_flights": len(flights),
            "unique_airlines": len(airlines),
            "unique_departure_airports": len(airports_from),
            "unique_arrival_airports": len(airports_to),
            "average_flight_duration_minutes": round(avg_duration) if avg_duration else None,
            "airlines": airlines,
            "airports_from": airports_from,
            "airports_to": airports_to,
            "aircraft_models": aircraft_models,
            "flight_statuses": flight_statuses,
            "durations": durations
        }