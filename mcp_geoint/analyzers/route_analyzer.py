"""
航线分析器

负责分析航线效率、热门目的地和网络多样性
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from collections import Counter, defaultdict
from ..utils import safe_divide

logger = logging.getLogger(__name__)


class RouteAnalyzer:
    """航线分析器"""
    
    @staticmethod
    def analyze_efficiency_metrics(flights: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算航线效率指标
        
        Args:
            flights: 航班数据列表
            
        Returns:
            包含效率指标的字典
        """
        if not flights:
            return {}
        
        # 统计航班状态
        status_counts = Counter(flight.get("status", "unknown") for flight in flights)
        total_flights = len(flights)
        
        # 计算成功率（非取消航班）
        cancelled_flights = status_counts.get("cancelled", 0)
        success_rate = round(safe_divide(total_flights - cancelled_flights, total_flights) * 100, 2)
        
        # 统计航空公司效率（按准点率排序）
        airline_performance = defaultdict(lambda: {"total": 0, "on_time": 0, "delayed": 0})
        
        for flight in flights:
            airline = flight.get("airline_name", "Unknown")
            scheduled = flight.get("departure_scheduled_time")
            actual = flight.get("departure_actual_time")
            
            airline_performance[airline]["total"] += 1
            
            if scheduled and actual:
                try:
                    sched_dt = datetime.fromisoformat(scheduled.replace('Z', '+00:00'))
                    actual_dt = datetime.fromisoformat(actual.replace('Z', '+00:00'))
                    
                    delay_minutes = (actual_dt - sched_dt).total_seconds() / 60
                    if delay_minutes > 15:
                        airline_performance[airline]["delayed"] += 1
                    else:
                        airline_performance[airline]["on_time"] += 1
                except (ValueError, TypeError) as e:
                    logger.debug(f"Failed to parse times for airline {airline} performance - scheduled: '{scheduled}', actual: '{actual}': {e}")
                    pass
        
        # 计算每个航空公司的准点率
        airline_punctuality = {}
        for airline, perf in airline_performance.items():
            analyzed = perf["on_time"] + perf["delayed"]
            if analyzed > 0:
                punctuality = round(safe_divide(perf["on_time"], analyzed) * 100, 2)
                airline_punctuality[airline] = {
                    "total_flights": perf["total"],
                    "analyzed_flights": analyzed,
                    "punctuality_rate_percent": punctuality
                }
        
        # 排序航空公司（按准点率降序）
        sorted_airlines = sorted(
            airline_punctuality.items(), 
            key=lambda x: x[1]["punctuality_rate_percent"], 
            reverse=True
        )[:10]
        
        return {
            "flight_status_distribution": dict(status_counts),
            "cancelled_flights": cancelled_flights,
            "top_performing_airlines": dict(sorted_airlines),
            "total_airlines_analyzed": len(airline_punctuality)
        }
    
    @staticmethod
    def analyze_popular_destinations(flights: List[Dict[str, Any]], direction: str) -> Dict[str, Any]:
        """
        分析最热门的城市和国家目的地
        
        Args:
            flights: 航班数据列表
            direction: 分析方向 ('from' 或 'to')
            
        Returns:
            包含最热门城市和国家的字典
        """
        city_counter = Counter()
        country_counter = Counter()
        international_flights = []
        domestic_flights = []

        if direction == 'from':
            location_key = 'to_airport_info'
        else:
            location_key = 'from_airport_info'

        for flight in flights:
            airport_info = flight.get(location_key)
            if airport_info and isinstance(airport_info, dict):
                # 提取城市信息
                city = airport_info.get('prefecture_en')
                if not city:
                    # 如果没有 prefecture_en，尝试从 name_en 提取第一个词作为城市名
                    name_en = airport_info.get('name_en')
                    if name_en and isinstance(name_en, str) and name_en.strip():
                        city_parts = name_en.strip().split()
                        if city_parts:
                            city = city_parts[0]
                
                if city and city != 'Unknown':
                    city_counter[city] += 1
                
                # 提取国家信息
                country = airport_info.get('country')
                if country and country != 'Unknown':
                    country_counter[country] += 1

            # 检查是否为国际航班
            is_international = flight.get('as_international')
            if is_international is None:
                is_international = flight.get('is_international', False)
            
            if is_international:
                international_flights.append(flight)
            else:
                domestic_flights.append(flight)

        # 分析国际航班的热门国家
        intl_country_counter = Counter()
        for flight in international_flights:
            airport_info = flight.get(location_key)
            if airport_info and isinstance(airport_info, dict):
                country = airport_info.get('country')
                if country and country != 'Unknown':
                    intl_country_counter[country] += 1

        return {
            "most_popular_cities": dict(city_counter.most_common(10)),
            "most_popular_countries": dict(country_counter.most_common(10)),
            "most_popular_international_countries": dict(intl_country_counter.most_common(10)) if international_flights else {},
            "has_international_flights": len(international_flights) > 0,
            "international_flights_count": len(international_flights),
            "domestic_flights_count": len(domestic_flights)
        }
    
    @staticmethod
    def analyze_network_and_diversity(flights: List[Dict[str, Any]], direction: str) -> Dict[str, Any]:
        """
        分析航线网络、国际/国内构成以及航空公司运营多样性
        
        Args:
            flights: 航班数据列表
            direction: 分析方向 ('from' 或 'to')
            
        Returns:
            包含航线网络和航空公司多样性分析的字典
        """
        route_counter = Counter()
        international_flights = []
        domestic_flights = []
        airline_routes = defaultdict(set)
        airline_aircrafts = defaultdict(set)

        if direction == 'from':
            # 从机场信息中提取城市名称
            location_key = 'to_airport_info'
            airport_name_key = 'name'
            iata_key = 'arrival_airport_iata'
        else:
            location_key = 'from_airport_info'
            airport_name_key = 'name'
            iata_key = 'departure_airport_iata'

        # 添加调试信息
        logger.info(f"分析 {len(flights)} 个航班，方向: {direction}")
        
        for flight in flights:
            # 从机场信息中提取城市/机场名称
            airport_info = flight.get(location_key)
            route_location = None
            if airport_info and isinstance(airport_info, dict):
                # 优先使用中文名称，如果没有则使用英文名称
                route_location = airport_info.get('name') or airport_info.get('name_en')
            
            if route_location:
                route_counter[route_location] += 1

            airline = flight.get('airline_name')
            
            # 处理飞机型号信息 - aircraft_info 可能为 null
            aircraft = None
            aircraft_info = flight.get("aircraft_info")
            if aircraft_info and isinstance(aircraft_info, dict):
                aircraft = aircraft_info.get("model_name")
            if not aircraft:
                aircraft = flight.get('aircraft_model')
                
            if airline and route_location:
                airline_routes[airline].add(route_location)
            if airline and aircraft:
                airline_aircrafts[airline].add(aircraft)

            # 检查国际/国内标志 - 使用正确的字段名 as_international
            is_international = flight.get('as_international')
            
            # 如果 as_international 字段不存在或为 None，尝试其他判断方法
            if is_international is None:
                # 尝试旧字段名
                is_international = flight.get('is_international')
                if is_international is None:
                    logger.debug(f"航班 {flight.get('flight_number_iata', 'Unknown')} 缺少 as_international 字段")
                    is_international = False  # 默认为国内
            
            if is_international:
                international_flights.append(flight)
            else:
                domestic_flights.append(flight)

        # 航空公司多样性分析
        airline_diversity = []
        for airline, routes in airline_routes.items():
            airline_diversity.append({
                "airline": airline,
                "routes_operated": len(routes),
                "aircraft_models_used": len(airline_aircrafts[airline])
            })
        
        # 按执飞航线数量排序
        airline_diversity_sorted = sorted(airline_diversity, key=lambda x: x['routes_operated'], reverse=True)

        # 添加调试信息
        logger.info(f"国际航班: {len(international_flights)}, 国内航班: {len(domestic_flights)}")

        # 分别统计国际和国内热门航线
        intl_route_counter = Counter()
        dom_route_counter = Counter()
        
        for f in international_flights:
            airport_info = f.get(location_key)
            if airport_info and isinstance(airport_info, dict):
                route_name = airport_info.get('name') or airport_info.get('name_en')
                if route_name:
                    intl_route_counter[route_name] += 1
        
        for f in domestic_flights:
            airport_info = f.get(location_key)
            if airport_info and isinstance(airport_info, dict):
                route_name = airport_info.get('name') or airport_info.get('name_en')
                if route_name:
                    dom_route_counter[route_name] += 1

        return {
            "top_routes": dict(route_counter.most_common(10)),
            "international_domestic_split": {
                "international_flights": len(international_flights),
                "domestic_flights": len(domestic_flights),
                "total_flights": len(flights),
                "international_percentage": round(safe_divide(len(international_flights), len(flights)) * 100, 2),
                "top_international_routes": dict(intl_route_counter.most_common(10)),
                "top_domestic_routes": dict(dom_route_counter.most_common(10)),
            },
            "airline_operational_diversity": airline_diversity_sorted[:10]
        }