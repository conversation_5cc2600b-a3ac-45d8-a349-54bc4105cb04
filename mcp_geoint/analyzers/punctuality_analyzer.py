"""
准点率分析器

负责计算航班准点率相关指标
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from statistics import mean
from ..utils import safe_divide

logger = logging.getLogger(__name__)


class PunctualityAnalyzer:
    """准点率分析器"""
    
    @staticmethod
    def calculate_metrics(flights: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算准点率相关指标
        
        Args:
            flights: 航班数据列表
            
        Returns:
            包含准点率分析的字典
        """
        if not flights:
            return {}
        
        on_time_flights = 0
        delayed_flights = 0
        delay_durations = []
        
        for flight in flights:
            scheduled = flight.get("departure_scheduled_time")
            actual = flight.get("departure_actual_time")
            
            if scheduled and actual:
                try:
                    sched_dt = datetime.fromisoformat(scheduled.replace('Z', '+00:00'))
                    actual_dt = datetime.fromisoformat(actual.replace('Z', '+00:00'))
                    
                    delay_minutes = (actual_dt - sched_dt).total_seconds() / 60
                    delay_durations.append(delay_minutes)
                    
                    # 延误超过 15 分钟算作延误
                    if delay_minutes > 15:
                        delayed_flights += 1
                    else:
                        on_time_flights += 1
                except (ValueError, TypeError) as e:
                    logger.debug(f"Failed to parse time for punctuality analysis - scheduled: '{scheduled}', actual: '{actual}': {e}")
                    pass
        
        total_analyzed = on_time_flights + delayed_flights
        punctuality_rate = None
        avg_delay = None
        
        if total_analyzed > 0:
            punctuality_rate = round(safe_divide(on_time_flights, total_analyzed) * 100, 2)
        
        if delay_durations:
            avg_delay = round(mean(delay_durations), 2)
        
        return {
            "on_time_flights": on_time_flights,
            "delayed_flights": delayed_flights,
            "punctuality_rate_percent": punctuality_rate,
            "average_delay_minutes": avg_delay,
            "total_analyzed_flights": total_analyzed
        }