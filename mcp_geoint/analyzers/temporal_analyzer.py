"""
时间模式分析器

负责分析航班的时间分布和模式
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from collections import defaultdict

logger = logging.getLogger(__name__)


class TemporalAnalyzer:
    """时间模式分析器"""
    
    @staticmethod
    def analyze_patterns(flights: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析航班的时间模式（日期分布、小时分布）
        
        Args:
            flights: 航班数据列表
            
        Returns:
            包含时间模式分析的字典
        """
        if not flights:
            return {}
        
        daily_flights = defaultdict(int)
        hourly_departures = defaultdict(int)
        
        for flight in flights:
            departure_time = flight.get("departure_scheduled_time")
            if departure_time:
                try:
                    dt = datetime.fromisoformat(departure_time.replace('Z', '+00:00'))
                    date_key = dt.strftime('%Y-%m-%d')
                    daily_flights[date_key] += 1
                    
                    hour_key = dt.strftime('%H:00')
                    hourly_departures[hour_key] += 1
                except (ValueError, TypeError) as e:
                    logger.debug(f"Failed to parse departure time '{departure_time}': {e}")
                    pass
        
        # 找出最繁忙的日期和时间
        busiest_date = max(daily_flights.items(), key=lambda x: x[1]) if daily_flights else None
        busiest_hour = max(hourly_departures.items(), key=lambda x: x[1]) if hourly_departures else None
        
        return {
            "daily_flights": daily_flights,
            "hourly_departures": hourly_departures,
            "busiest_date": busiest_date,
            "busiest_hour": busiest_hour,
            "daily_average": round(len(flights) / max(len(daily_flights), 1), 2)
        }