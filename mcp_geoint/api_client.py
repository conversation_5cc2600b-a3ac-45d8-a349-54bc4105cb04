"""
API客户端模块

封装所有与外部航班数据API的网络请求
"""

import httpx
import logging
from typing import Dict, Any, Optional
from .config import (
    API_BASE_URL,
    SEARCH_API_BASE_URL,
    FLIGHTS_API_BETWEEN_CITIES,
    FLIGHTS_API_CITY_TO_COUNTRY,
    FLIGHTS_API_COUNTRY_TO_CITY,
    FLIGHTS_API_MATRIX_CITIES,
    HTTP_TIMEOUT
)
from .utils import clean_airport_name

logger = logging.getLogger(__name__)

# 全局 HTTP 客户端
http_client = httpx.AsyncClient(timeout=HTTP_TIMEOUT)


async def search_airport_by_name(airport_name: str) -> Optional[Dict[str, Any]]:
    """
    通过机场名称搜索获取机场信息，包括ICAO和IATA代码
    
    Args:
        airport_name: 机场名称（支持中文和英文模糊查询）
        
    Returns:
        机场信息字典，包含icao_code, iata_code, name等，如果未找到则返回None
    """
    try:
        # 清理机场名称，去除常见后缀
        cleaned_name = clean_airport_name(airport_name)
        logger.info(f"搜索机场: 原始名称='{airport_name}', 清理后='{cleaned_name}'")

        params = {
            "q": cleaned_name,
            "types": "airport"
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(SEARCH_API_BASE_URL, params=params)
            response.raise_for_status()
            data = response.json()

            airports = data.get("results", {}).get("airports", [])
            if airports:
                # 返回第一个匹配的机场
                airport = airports[0]
                logger.info(
                    f"找到机场: {airport.get('name')} (ICAO: {airport.get('icao_code')}, IATA: {airport.get('iata_code')})")
                return airport
            else:
                # 如果清理后的名称没找到，尝试使用原始名称
                if cleaned_name != airport_name:
                    logger.info(f"使用清理后名称未找到，尝试原始名称: {airport_name}")
                    params["q"] = airport_name
                    response = await client.get(SEARCH_API_BASE_URL, params=params)
                    response.raise_for_status()
                    data = response.json()

                    airports = data.get("results", {}).get("airports", [])
                    if airports:
                        airport = airports[0]
                        logger.info(
                            f"使用原始名称找到机场: {airport.get('name')} (ICAO: {airport.get('icao_code')}, IATA: {airport.get('iata_code')})")
                        return airport

                logger.warning(f"未找到机场: {airport_name}")
                return None
            
    except Exception as e:
        logger.error(f"搜索机场 {airport_name} 时出错: {e}")
        return None


async def resolve_airport_location(location: str, location_type: str) -> tuple[str, str]:
    """
    解析机场位置参数，如果是机场名称则搜索获取ICAO代码
    
    Args:
        location: 位置名称（可能是机场名称或代码）
        location_type: 位置类型
        
    Returns:
        (resolved_location, actual_location_type) 元组
    """
    if location_type == "airport":
        # 检查是否已经是ICAO或IATA代码（通常是3-4个字符，主要是字母，可能包含数字）
        if (location and len(location) >= 3 and len(location) <= 4 and
            location.isalnum() and location.isupper() and
            any(c.isalpha() for c in location)):
            # 已经是代码，直接使用
            return location, location_type
        else:
            # 是机场名称，需要搜索获取代码
            airport_info = await search_airport_by_name(location)
            if airport_info:
                # 优先使用ICAO代码，如果没有则使用IATA代码
                icao_code = airport_info.get("icao_code")
                iata_code = airport_info.get("iata_code")
                resolved_code = icao_code if icao_code else iata_code
                if resolved_code:
                    logger.info(f"机场名称 '{location}' 解析为代码: {resolved_code}")
                    return resolved_code, location_type
                else:
                    logger.warning(f"机场 '{location}' 没有有效的ICAO或IATA代码")
                    return location, location_type
            else:
                logger.warning(f"无法找到机场: {location}，将使用原始名称")
                return location, location_type
    
    # 非机场类型，直接返回
    return location, location_type


async def make_location_flight_request(
    location: str,
    location_type: str,
    date_from: str,
    date_to: str,
    direction: str,
    is_international: Optional[bool] = None
) -> Dict[str, Any]:
    """
    发送单个地点的航班查询请求到相应的 API 端点，并自动处理分页以获取所有数据。

    Args:
        location: 地点名称（城市、国家或机场）
        location_type: 地点类型 ('city', 'country', 'airport')
        date_from: 开始日期 (YYYY-MM-DD)
        date_to: 结束日期 (YYYY-MM-DD)
        direction: 分析方向 ('from' 或 'to')
        is_international: 是否只看国际航班，None=全部, True=仅国际, False=仅国内

    Returns:
        包含所有航班结果的列表

    Raises:
        Exception: 当 API 请求失败时
    """
    # 构造请求参数
    API_PAGE_SIZE = 1000
    params = {
        "date_from": date_from,
        "date_to": date_to,
        "location": location,
        "location_type": location_type,
        "page_size": API_PAGE_SIZE,
    }
    if is_international is not None:
        params["is_international"] = str(is_international)

    # 构造请求URL
    endpoint = f"/api/flights-expanded/{direction}-location/"
    url = f"{API_BASE_URL}{endpoint}"

    try:
        # 发送API请求并处理分页
        all_flights = []
        # 使用全局客户端，但设置更长的超时时间
        original_timeout = http_client.timeout
        http_client.timeout = httpx.Timeout(300.0)

        try:
            while url:
                logger.info(f"Fetching data from {url} with params: {params}")
                response = await http_client.get(url, params=params)
                params = None  # 只在第一次请求时使用params
                response.raise_for_status()
                data = response.json()
                all_flights.extend(data.get("results", []))
                url = data.get("next")
        finally:
            # 恢复原始超时设置
            http_client.timeout = original_timeout

        return all_flights
        
    except httpx.HTTPError as e:
        raise Exception(f"HTTP 请求失败: {e}")
    except Exception as e:
        raise Exception(f"处理航班数据时出错: {e}")


async def make_flight_request(
    origin_location: str,
    origin_type: str,
    destination_location: str,
    destination_type: str,
    date_from: str,
    date_to: str,
    page_size: int = 1000
) -> Optional[Dict[str, Any]]:
    """
    获取两地之间的航班数据，支持自动分页获取所有数据
    
    Args:
        origin_location: 出发地
        origin_type: 出发地类型 ('city', 'country')
        destination_location: 目的地
        destination_type: 目的地类型 ('city', 'country')
        date_from: 开始日期
        date_to: 结束日期
        page_size: 每页返回结果限制
        
    Returns:
        包含航班数据的字典，格式: {"count": int, "results": list}
    """
    try:
        # 根据类型组合确定API端点和参数名
        if origin_type == 'city' and destination_type == 'city':
            api_url = FLIGHTS_API_BETWEEN_CITIES
            params = {
                "from_city": origin_location,
                "to_city": destination_location,
                "date_from": date_from,
                "date_to": date_to,
                "page": 1,
                "page_size": page_size
            }
        elif origin_type == 'city' and destination_type == 'country':
            api_url = FLIGHTS_API_CITY_TO_COUNTRY
            params = {
                "from_city": origin_location,
                "to_country": destination_location,
                "date_from": date_from,
                "date_to": date_to,
                "page": 1,
                "page_size": page_size
            }
        elif origin_type == 'country' and destination_type == 'city':
            api_url = FLIGHTS_API_COUNTRY_TO_CITY
            params = {
                "from_country": origin_location,
                "to_city": destination_location,
                "date_from": date_from,
                "date_to": date_to,
                "page": 1,
                "page_size": page_size
            }
        else:
            logger.error(f"不支持的查询类型组合: {origin_type} -> {destination_type}")
            return None
        
        logger.info(f"请求两地航班数据: {api_url} with params: {params}")
        
        # 发送初始请求
        response = await http_client.get(api_url, params=params)
        response.raise_for_status()
        
        initial_data = response.json()
        
        # 处理不同的响应格式并实现分页
        if isinstance(initial_data, list):
            # 如果API直接返回列表，包装成标准格式（无分页）
            result = {"count": len(initial_data), "results": initial_data}
        elif isinstance(initial_data, dict):
            total_count = initial_data.get("count", 0)
            all_flights = initial_data.get("results", [])
            
            # 如果有更多数据需要分页获取
            if total_count > page_size:
                import math
                import asyncio
                
                total_pages = math.ceil(total_count / page_size)
                logger.info(f"总共 {total_count} 条数据，需要获取 {total_pages} 页")
                
                # 并发获取剩余页面
                tasks = []
                for page_num in range(2, total_pages + 1):
                    page_params = params.copy()
                    page_params["page"] = page_num
                    tasks.append(http_client.get(api_url, params=page_params))
                
                if tasks:
                    responses = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    for resp in responses:
                        if isinstance(resp, Exception):
                            logger.error(f"分页请求失败: {resp}")
                            continue
                        try:
                            resp.raise_for_status()
                            page_data = resp.json()
                            if isinstance(page_data, dict):
                                all_flights.extend(page_data.get("results", []))
                            elif isinstance(page_data, list):
                                all_flights.extend(page_data)
                        except Exception as e:
                            logger.error(f"处理分页响应失败: {e}")
            
            # 构建最终结果
            result = {"count": len(all_flights), "results": all_flights}
        else:
            result = {"count": 0, "results": []}
        
        logger.info(f"总共获取到 {result['count']} 条航班数据")
        
        return result
        
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP错误 - 获取两地航班数据: {e}")
        return None
    except Exception as e:
        logger.error(f"获取两地航班数据失败: {e}")
        return None


async def fetch_flight_matrix_data(cities: str, date_from: str, date_to: str) -> Optional[Dict[str, Any]]:
    """
    获取城市矩阵航班数据
    
    Args:
        cities: 城市列表，用逗号分隔的字符串
        date_from: 开始日期
        date_to: 结束日期
        
    Returns:
        矩阵数据字典
    """
    try:
        # 构造请求参数（使用GET请求，与原始实现一致）
        params = {
            "cities": cities,
            "date_from": date_from,
            "date_to": date_to
        }
        
        logger.info(f"请求城市矩阵数据: {FLIGHTS_API_MATRIX_CITIES} with params: {params}")
        
        response = await http_client.get(FLIGHTS_API_MATRIX_CITIES, params=params)
        response.raise_for_status()
        
        data = response.json()
        logger.info(f"获取到矩阵数据，包含 {len(data.get('results', [])) if isinstance(data, dict) else 0} 个条目")
        
        return data if isinstance(data, dict) else {}
        
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP错误 - 获取城市矩阵数据: {e}")
        return None
    except Exception as e:
        logger.error(f"获取城市矩阵数据失败: {e}")
        return None


async def cleanup():
    """清理HTTP客户端资源"""
    await http_client.aclose()