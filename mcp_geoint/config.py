"""
配置管理模块

集中管理所有配置、环境变量和API URL
"""

import os
import logging
from dotenv import load_dotenv

# 从 .env 文件加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API 配置
API_BASE_URL = os.getenv("API_BASE_URL", "http://127.0.0.1:8000")
FLIGHTS_API_BASE_URL = os.getenv("FLIGHTS_API_BASE_URL", f"{API_BASE_URL}/api/flights/")
FLIGHTS_API_BETWEEN_CITIES = f"{FLIGHTS_API_BASE_URL}between-cities/"
FLIGHTS_API_CITY_TO_COUNTRY = f"{FLIGHTS_API_BASE_URL}city-to-country/"
FLIGHTS_API_COUNTRY_TO_CITY = f"{FLIGHTS_API_BASE_URL}country-to-city/"
FLIGHTS_API_MATRIX_CITIES = os.getenv("FLIGHTS_API_MATRIX_CITIES", f"{API_BASE_URL}/api/flights/matrix/cities/")
SEARCH_API_BASE_URL = os.getenv("SEARCH_API_BASE_URL", f"{API_BASE_URL}/api/search/locations/")

# 验证必要的环境变量
if not FLIGHTS_API_BASE_URL:
    raise ValueError("FLIGHTS_API_BASE_URL 环境变量未设置，请在 .env 文件中或直接在环境中定义。")

# HTTP 客户端配置
HTTP_TIMEOUT = 30.0