#!/usr/bin/env python3
"""
GEOINT Flights MCP Server - 航班地理情报分析平台

重构后的简化启动入口
"""

import asyncio
import logging
from mcp_geoint.tools import mcp, cleanup
from mcp_geoint.config import logger

if __name__ == "__main__":
    try:
        # 使用更灵活的方法配置路径以适应nginx代理
        import uvicorn
        from fastmcp.server.http import create_sse_app
        
        logger.info("启动 GEOINT Flights MCP Server...")
        
        # 创建SSE应用，配置正确的路径前缀
        app = create_sse_app(
            server=mcp,
            sse_path="/mcp/sse",
            message_path="/mcp/messages/"
        )
        
        # 使用uvicorn直接运行
        uvicorn.run(app, host="0.0.0.0", port=8005)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
    finally:
        logger.info("清理资源...")
        asyncio.run(cleanup())