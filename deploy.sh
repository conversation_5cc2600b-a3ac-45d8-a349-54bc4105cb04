#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# The name of the Docker image archive you will upload to the server.
# IMPORTANT: Make sure the file you upload matches this name.
IMAGE_ARCHIVE="mcp-geoint-flights.tar.gz"

# The path to your docker-compose file.
COMPOSE_FILE="docker-compose.yml"

# --- Deployment Steps ---

echo "🚀 Starting deployment..."

# Check if the image archive exists before starting
if [ ! -f "$IMAGE_ARCHIVE" ]; then
    echo "❌ Error: Image archive '$IMAGE_ARCHIVE' not found!"
    echo "Please upload the image archive to the same directory as this script first."
    exit 1
fi

echo "1. Stopping and removing existing containers..."
# 'down' stops and removes containers, networks, and volumes defined in the compose file.
docker-compose -f $COMPOSE_FILE down

echo "2. Loading new Docker image from '$IMAGE_ARCHIVE'..."
# Loads the image from the tar archive.
docker load -i $IMAGE_ARCHIVE

echo "3. Starting services with the new image..."
# '-d' runs containers in detached mode.
# Docker Compose will use the newly loaded image if the tag in docker-compose.yml matches.
docker-compose -f $COMPOSE_FILE up -d

echo "4. Cleaning up old and unused Docker images..."
# 'docker image prune' removes all dangling images (images without a tag).
# The '-f' flag forces the removal without prompting for confirmation.
docker image prune -f

echo "✅ Deployment finished successfully!"
