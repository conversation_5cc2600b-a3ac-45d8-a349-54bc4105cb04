# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media

# Virtual Environment
venv/
.venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo
.cursor/
.windsurf/
.claude/

# Coverage
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# macOS
.DS_Store

# PostgreSQL
*.dump

# Static files
staticfiles/

docker-images/